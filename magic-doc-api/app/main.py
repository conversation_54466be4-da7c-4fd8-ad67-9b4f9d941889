from fastapi import FastAP<PERSON>, UploadFile, File, Form
from fastapi.responses import PlainTextResponse, StreamingResponse
import os, tempfile, io, zipfile, shutil
from magic_doc.docconv import DocConverter

app = FastAPI(title="Magic-Doc API")

def _zip_dir(path: str) -> io.BytesIO:
    buf = io.BytesIO()
    with zipfile.ZipFile(buf, "w", zipfile.ZIP_DEFLATED) as z:
        for root, _, files in os.walk(path):
            for f in files:
                fp = os.path.join(root, f)
                z.write(fp, os.path.relpath(fp, path))
    buf.seek(0)
    return buf

@app.post("/convert", response_class=PlainTextResponse)
async def convert(file: UploadFile = File(...), timeout: int = Form(300)):
    with tempfile.TemporaryDirectory() as tmp:
        inpath = os.path.join(tmp, file.filename)
        with open(inpath, "wb") as f:
            f.write(await file.read())
        md, secs = DocConverter(s3_config=None).convert(inpath, conv_timeout=timeout)
        return PlainTextResponse(md, headers={"X-Conversion-Time-Seconds": str(secs)})

@app.post("/convert_mid")
async def convert_mid(file: UploadFile = File(...), timeout: int = Form(300)):
    with tempfile.TemporaryDirectory() as tmp:
        inpath = os.path.join(tmp, file.filename)
        with open(inpath, "wb") as f:
            f.write(await file.read())

        conv = DocConverter(s3_config=None)
        md, secs = conv.convert_to_mid_result(inpath, conv_timeout=timeout)

        outdir = os.path.join(tmp, "out")
        os.makedirs(outdir, exist_ok=True)
        with open(os.path.join(outdir, "index.md"), "w", encoding="utf-8") as f:
            f.write(md)

        images_src = os.path.join(os.path.dirname(inpath), "images")  # Magic-Doc writes here
        if os.path.isdir(images_src):
            shutil.copytree(images_src, os.path.join(outdir, "images"))

        zipbuf = _zip_dir(outdir)
        headers = {
            "Content-Disposition": "attachment; filename=md_out.zip",
            "X-Conversion-Time-Seconds": str(secs),
        }
        return StreamingResponse(zipbuf, media_type="application/zip", headers=headers)

@app.get("/healthz")
def healthz():
    return {"status": "ok"}
