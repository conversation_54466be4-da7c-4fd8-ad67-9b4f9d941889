FROM python:3.10

ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    DEBIAN_FRONTEND=noninteractive

# LibreOffice is used under the hood (esp. for PPT/DOC). Add some common fonts too.
RUN apt-get update && \
    apt-get install -y --no-install-recommends libreoffice fonts-dejavu-core fonts-noto-cjk && \
    rm -rf /var/lib/apt/lists/*

WORKDIR /app
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

COPY app ./app
EXPOSE 3101
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "3101", "--workers", "1"]
