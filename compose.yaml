services:
  mineru-sglang-server:
    image: mineru-sglang:latest
    container_name: mineru-sglang-server
    restart: unless-stopped
    profiles: ["sglang-server"]
    ports:
      - "30000:30000"
    environment:
      - MINERU_MODEL_SOURCE=local
      - PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True
    entrypoint: ["mineru-sglang-server"]
    command: ["--host","0.0.0.0","--port","30000","--mem-fraction-static","0.3"]
    ulimits:
      memlock: -1
      stack: 67108864
    ipc: host
    gpus: "all"   # <-- Compose v2 way to request GPUs

  mineru-api:
    image: mineru-sglang:latest
    container_name: mineru-api
    restart: unless-stopped
    profiles: ["api"]
    ports:
      - "8000:8000"
    environment:
      - MINERU_MODEL_SOURCE=local
    entrypoint: ["mineru-api"]
    command: ["--host","0.0.0.0","--port","8000","--mem-fraction-static","0.3"]
    ulimits:
      memlock: -1
      stack: 67108864
    ipc: host
    # (No GPU here; let the sglang-server own the GPU)

  mineru-gradio:
    image: mineru-sglang:latest
    container_name: mineru-gradio
    restart: unless-stopped
    profiles: ["gradio"]
    ports:
      - "7860:7860"
    environment:
      - MINERU_MODEL_SOURCE=local
      - GRADIO_SERVER_NAME=0.0.0.0
      - GRADIO_SERVER_PORT=7860
    entrypoint: ["mineru-gradio"]
    # IMPORTANT: disable built-in sglang engine to avoid double-loading the model
    command: ["--server-name","0.0.0.0","--server-port","7860","--enable-sglang-engine","false"]
    ulimits:
      memlock: -1
      stack: 67108864
    ipc: host
    # (No GPU here either)


  magic-doc-api:
    build: ./magic-doc-api
    ports: ["3101:3101"]
    entrypoint: ["magic-doc"]
    command: ["app.main:app","--host","0.0.0.0","--port","3101","--workers","1"]
